# 🔧 端口不匹配问题解决方案

## 问题诊断
当前状态：
- ✅ **browser-tools-server**: 运行在端口 **3026**
- ✅ **browser-tools-mcp**: 正常运行，已连接到端口 3025 的服务
- ❌ **Chrome扩展**: 默认连接端口 3025，但需要连接到 3026

## 🚀 立即解决方案

### 方案1：重新配置Chrome扩展端口（推荐）

1. **打开Chrome扩展管理**:
   ```
   chrome://extensions/
   ```

2. **重新加载扩展**:
   - 找到 "BrowserTools MCP" 扩展
   - 点击刷新按钮重新加载

3. **配置正确端口**:
   - 打开任意网页
   - 按 F12 打开开发者工具
   - 找到 "BrowserTools" 标签页
   - 在 "Server Connection Settings" 部分:
     - Server Host: 保持 `localhost`
     - **Server Port: 将 `3025` 改为 `3026`**
   - 点击 "Test Connection" 按钮验证连接
   - 连接成功后状态指示器会变绿

### 方案2：停止占用3025端口的服务

如果您不需要3025端口的服务，可以：

```powershell
# 查找占用3025端口的进程
netstat -ano | findstr :3025

# 停止进程（替换PID为实际进程ID）
taskkill /F /PID 24108
```

然后重启browser-tools-server，它会自动使用3025端口。

## 🔍 验证连接

连接成功后，在BrowserTools面板中应该看到：
- 连接状态显示为 "已连接"
- 可以成功截取屏幕截图
- 控制台日志功能正常

## 📞 如果仍有问题

请检查：
1. Chrome扩展是否正确安装在开发者模式下
2. 服务器端口配置是否正确
3. 防火墙是否阻止了连接
4. 浏览器控制台是否有错误信息

---

**注意**: 端口不匹配是常见问题，按照上述步骤通常可以快速解决。