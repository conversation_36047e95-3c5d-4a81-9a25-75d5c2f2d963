# BrowserTools MCP 扩展故障排除指南

## 🚨 当前问题诊断

根据您的描述，问题可能是：
1. **端口不匹配**: 扩展默认连接3025端口，但服务器运行在3026端口
2. **扩展未正确安装**
3. **权限问题**

## 🔧 解决步骤

### 步骤1: 重新安装Chrome扩展

1. **卸载现有扩展**:
   - 打开 `chrome://extensions/`
   - 找到 "BrowserTools MCP" 扩展
   - 点击 "移除" 按钮

2. **重新安装扩展**:
   - 确保 "开发者模式" 已开启（右上角开关）
   - 点击 "加载已解压的扩展程序"
   - 选择文件夹: `C:\Users\<USER>\Downloads\mcp-main\browser-tools-mcp\BrowserTools-Extension\chrome-extension`
   - 确认扩展已成功安装

### 步骤2: 配置正确的端口

1. **打开任意网页** (如 https://example.com)
2. **按F12打开开发者工具**
3. **查找BrowserTools标签页**:
   - 如果看不到，点击开发者工具右上角的 ">>" 按钮
   - 在下拉菜单中找到 "BrowserTools"

4. **配置服务器设置**:
   - 在BrowserTools面板中找到设置区域
   - 将服务器端口从 `3025` 改为 `3026`
   - 主机保持 `localhost`

### 步骤3: 验证连接

1. **检查服务器状态**:
   ```
   服务器应该显示: "Aggregator listening on http://0.0.0.0:3026"
   ```

2. **测试连接**:
   - 在BrowserTools面板中应该看到连接状态
   - 状态应该显示为 "已连接" 或 "Connected"

## 🔍 详细检查清单

### ✅ 服务器检查
- [ ] browser-tools-server 正在运行 (端口3026)
- [ ] browser-tools-mcp 正在运行
- [ ] 没有防火墙阻止本地连接

### ✅ 扩展检查
- [ ] 扩展已正确安装在Chrome中
- [ ] 扩展权限已授予
- [ ] 开发者工具已打开
- [ ] BrowserTools标签页可见

### ✅ 配置检查
- [ ] 服务器主机: localhost
- [ ] 服务器端口: 3026
- [ ] 扩展设置已保存

## 🚀 快速测试

安装完成后，尝试以下操作：

1. **打开网页**: https://example.com
2. **按F12**: 打开开发者工具
3. **找到BrowserTools标签页**
4. **点击 "Take Screenshot"** 按钮
5. **检查控制台**: 应该看到成功消息

## 🆘 如果仍然有问题

### 检查Chrome控制台错误
1. 在扩展页面 (`chrome://extensions/`) 点击扩展的 "详细信息"
2. 查看是否有错误信息
3. 检查 "检查视图" 中的控制台日志

### 重启所有组件
1. 关闭所有Chrome窗口
2. 停止所有服务器 (Ctrl+C)
3. 重新启动服务器
4. 重新打开Chrome和扩展

### 端口冲突检查
```powershell
# 检查端口3026是否被占用
netstat -ano | findstr :3026
```

## 📝 常见错误信息

- **"Chrome extension not connected"**: 扩展未连接到服务器
- **"Connection refused"**: 端口配置错误或服务器未运行
- **"Permission denied"**: 扩展权限不足

## 🎯 成功标志

当一切正常工作时，您应该看到：
- ✅ BrowserTools标签页在开发者工具中可见且可点击
- ✅ 连接状态显示为已连接
- ✅ 可以成功截取屏幕截图
- ✅ 可以查看控制台日志和网络请求

---

**提示**: 如果按照以上步骤仍然无法解决问题，请检查Chrome版本是否支持Manifest V3扩展。