# 🔧 Chrome扩展安装详细指南

## 问题诊断
如果在开发者工具中找不到 "BrowserTools" 标签页，说明Chrome扩展可能没有正确安装。

## 📋 完整安装步骤

### 1. 打开Chrome扩展管理页面
```
chrome://extensions/
```

### 2. 启用开发者模式
- 在页面右上角找到 "开发者模式" 开关
- 确保开关处于 **开启** 状态

### 3. 加载扩展
- 点击 "加载已解压的扩展程序" 按钮
- 选择文件夹: `C:\Users\<USER>\Downloads\mcp-main\browser-tools-mcp\BrowserTools-Extension\chrome-extension`
- 点击 "选择文件夹"

### 4. 验证安装
安装成功后，您应该看到：
- 扩展列表中出现 "BrowserTools MCP" 扩展
- 扩展状态显示为 "已启用"
- 扩展ID和版本信息

### 5. 测试扩展
- 打开任意网页（如 https://example.com）
- 按 **F12** 打开开发者工具
- 在开发者工具的标签页中寻找 **"BrowserTools"** 标签

## 🚨 常见问题

### 问题1：找不到 "BrowserTools" 标签页
**可能原因**：
- 扩展未正确安装
- 扩展被禁用
- 需要刷新页面

**解决方案**：
1. 检查 `chrome://extensions/` 中扩展状态
2. 确保扩展已启用
3. 刷新当前网页
4. 重新打开开发者工具

### 问题2：扩展安装失败
**可能原因**：
- 选择了错误的文件夹
- manifest.json文件损坏

**解决方案**：
1. 确保选择的是 `chrome-extension` 文件夹
2. 检查文件夹中是否包含 `manifest.json`
3. 重新解压扩展文件

### 问题3：扩展显示错误
**可能原因**：
- 文件权限问题
- Chrome版本不兼容

**解决方案**：
1. 以管理员身份运行Chrome
2. 更新Chrome到最新版本
3. 重新下载扩展文件

## 📞 验证步骤

扩展正确安装后：
1. 开发者工具中应该有 "BrowserTools" 标签页
2. 点击该标签页应该显示扩展界面
3. 界面中应该有 "Server Connection Settings" 部分
4. 可以看到端口配置选项（默认3025）

## 🎯 下一步

扩展安装成功后，按照端口配置指南将端口从3025改为3026即可。

---

**提示**: 如果仍然有问题，请检查Chrome控制台是否有错误信息，或尝试重启Chrome浏览器。