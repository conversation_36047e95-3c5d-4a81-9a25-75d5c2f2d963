# Chrome扩展连接错误修复方案

## 错误分析

您遇到的错误：`Uncaught (in promise) Error: Could not establish connection. Receiving end does not exist.`

这是典型的Chrome扩展端口不匹配问题。

### 问题根源
1. **服务器运行在端口3026**（因为3025被占用）
2. **扩展默认尝试连接3025端口**
3. **端口不匹配导致连接失败**

## 🔧 立即修复步骤

### 步骤1：确认服务器状态
✅ **服务器正常运行**：
- browser-tools-server: `http://localhost:3026`
- 身份验证端点: `http://localhost:3026/.identity` ✅ 正常
- 返回签名: `mcp-browser-connector-24x7` ✅ 正确

### 步骤2：修复Chrome扩展连接

**在Chrome浏览器中：**

1. **打开任意网页**（如 https://example.com）

2. **按F12打开开发者工具**

3. **查找"BrowserTools"标签页**
   - 如果没有看到，说明扩展未正确安装
   - 请先按照安装指南重新安装扩展

4. **点击"BrowserTools"标签页**

5. **找到"Server Connection Settings"部分**

6. **修改端口配置**：
   ```
   Server Host: localhost
   Server Port: 3026  ← 重要：改为3026
   ```

7. **点击"Test Connection"按钮**

8. **验证连接成功**：
   - 应该显示："Connected to browser-tools-server v1.2.0 at localhost:3026"
   - 状态指示器变为绿色

## 🔍 详细诊断步骤

### 如果仍然无法连接：

#### 检查1：扩展是否正确安装
```
1. 访问 chrome://extensions/
2. 确认"BrowserTools MCP"扩展存在
3. 确认扩展已启用（开关为蓝色）
4. 确认"开发者模式"已开启
```

#### 检查2：扩展是否加载到开发者工具
```
1. 打开任意网页
2. 按F12打开开发者工具
3. 查看所有标签页
4. 应该看到"BrowserTools"标签页
```

#### 检查3：网络连接测试
在浏览器地址栏直接访问：
```
http://localhost:3026/.identity
```
应该返回JSON响应：
```json
{
  "port": 3026,
  "name": "browser-tools-server",
  "version": "1.2.0",
  "signature": "mcp-browser-connector-24x7"
}
```

## 🚨 常见错误和解决方案

### 错误1："Could not establish connection"
**原因**：端口不匹配
**解决**：将扩展端口改为3026

### 错误2："Receiving end does not exist"
**原因**：扩展未正确加载或端口错误
**解决**：
1. 重新安装扩展
2. 确认端口为3026
3. 刷新页面后重试

### 错误3：找不到"BrowserTools"标签页
**原因**：扩展未安装或未加载
**解决**：
1. 检查 chrome://extensions/
2. 重新加载扩展
3. 刷新网页

## 📋 完整验证清单

- [ ] 服务器运行在3026端口
- [ ] 扩展已安装并启用
- [ ] 开发者模式已开启
- [ ] 能看到"BrowserTools"标签页
- [ ] 端口配置为3026
- [ ] 连接测试成功
- [ ] 状态指示器为绿色

## 🎯 快速测试命令

在PowerShell中测试服务器：
```powershell
curl http://localhost:3026/.identity
```

应该返回状态码200和正确的JSON响应。

---

**如果按照以上步骤仍然无法解决，请提供以下信息：**
1. Chrome扩展管理页面截图
2. 开发者工具截图（显示所有标签页）
3. BrowserTools标签页截图（如果存在）
4. 浏览器控制台错误信息