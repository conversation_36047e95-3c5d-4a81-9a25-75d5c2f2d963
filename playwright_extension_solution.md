# Playwright浏览器扩展加载解决方案

## 问题说明
Playwright启动的是一个独立的浏览器实例，默认情况下不会加载用户Chrome中安装的扩展。这就是为什么在Playwright浏览器中看不到BrowserTools扩展的原因。

## 解决方案

### 方案1：使用用户的Chrome浏览器（推荐）

**直接在您的Chrome浏览器中操作：**

1. **打开您的Chrome浏览器**（不是Playwright的）
2. **按F12打开开发者工具**
3. **查找"BrowserTools"标签页**
   - 如果没有看到，说明扩展未正确安装
   - 按照之前的安装指南重新安装扩展

4. **配置端口连接**：
   - 在BrowserTools标签页中找到"Server Connection Settings"
   - 将"Server Port"从3025改为3026
   - 点击"Test Connection"验证连接

### 方案2：配置Playwright加载扩展（高级）

如果必须使用Playwright，需要特殊配置：

```javascript
// 启动时加载扩展的配置
const context = await browser.newContext({
  args: [
    '--load-extension=C:\\Users\\<USER>\\Downloads\\mcp-main\\browser-tools-mcp\\BrowserTools-Extension\\chrome-extension',
    '--disable-extensions-except=C:\\Users\\<USER>\\Downloads\\mcp-main\\browser-tools-mcp\\BrowserTools-Extension\\chrome-extension'
  ]
});
```

## 推荐操作步骤

### 立即可用的解决方案：

1. **关闭Playwright浏览器**
2. **打开您的Chrome浏览器**
3. **访问任意网页**（如 https://example.com）
4. **按F12打开开发者工具**
5. **查找"BrowserTools"标签页**
6. **配置端口为3026并测试连接**

### 验证步骤：

1. **检查扩展安装**：
   - 访问 `chrome://extensions/`
   - 确认"BrowserTools MCP"扩展已安装并启用
   - 确认"开发者模式"已开启

2. **测试连接**：
   - 在开发者工具的BrowserTools标签页中
   - Server Host: `localhost`
   - Server Port: `3026`
   - 点击"Test Connection"
   - 应该显示连接成功

## 常见问题

**Q: 为什么Playwright浏览器中没有扩展？**
A: Playwright启动的是隔离的浏览器实例，不会自动加载用户扩展。

**Q: 必须使用Playwright吗？**
A: 不是的。BrowserTools扩展设计用于任何Chrome浏览器，包括您的主浏览器。

**Q: 如何确认扩展正常工作？**
A: 在Chrome开发者工具中看到"BrowserTools"标签页，且能成功连接到端口3026。

## 服务器状态确认

当前服务器运行状态：
- ✅ browser-tools-server: 运行在端口3026
- ✅ browser-tools-mcp: 已连接
- ✅ MCP主服务: 正常运行

**建议：直接使用您的Chrome浏览器进行测试，这是最简单有效的方法。**