# BrowserTools MCP 完整配置指南

## 🎉 已完成的步骤

### 1. ✅ 下载并配置服务器
- **browser-tools-server**: 已启动在端口 3026
  - 访问地址: http://localhost:3026
- **browser-tools-mcp**: 已启动并连接到服务器

### 2. ✅ Chrome扩展已下载
- 扩展文件位置: `C:\Users\<USER>\Downloads\mcp-main\browser-tools-mcp\BrowserTools-Extension\chrome-extension`

### 3. ✅ Playwright配置完成
- 可以正常导航网页
- 可以截取屏幕截图
- 可以获取页面内容

## 🔧 需要手动完成的步骤

### 安装Chrome扩展
1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启"开发者模式"（右上角开关）
4. 点击"加载已解压的扩展程序"
5. 选择文件夹: `C:\Users\<USER>\Downloads\mcp-main\browser-tools-mcp\BrowserTools-Extension\chrome-extension`
6. 扩展安装完成后，打开任意网页
7. 按F12打开开发者工具
8. 在开发者工具中找到"BrowserTools"标签页

### 验证连接
1. 确保browser-tools-server正在运行（端口3026）
2. 确保Chrome扩展已安装并激活
3. 在开发者工具的BrowserTools面板中应该能看到连接状态

## 🧪 测试功能

### Playwright功能（已验证）
- ✅ 网页导航
- ✅ 屏幕截图
- ✅ 页面内容获取

### BrowserTools功能（需要Chrome扩展）
- 📋 控制台日志监控
- 🌐 网络请求监控
- 📸 浏览器截图
- 🔍 页面元素选择
- 🚀 性能审计
- ♿ 可访问性审计
- 🔍 SEO审计

## 🚀 使用示例

安装Chrome扩展后，您可以使用以下MCP工具：

```
# 获取控制台日志
getConsoleLogs

# 获取网络错误
getNetworkErrors

# 截取屏幕截图
takeScreenshot

# 运行性能审计
runPerformanceAudit

# 运行可访问性审计
runAccessibilityAudit
```

## 📝 注意事项

1. **两个服务器都需要运行**:
   - browser-tools-server (端口3026)
   - browser-tools-mcp (MCP服务器)

2. **Chrome扩展必须手动安装**

3. **开发者工具必须打开**才能使用BrowserTools功能

4. **Playwright独立工作**，不依赖Chrome扩展

## 🔗 相关链接

- [官方文档](https://browsertools.agentdesk.ai/)
- [GitHub仓库](https://github.com/AgentDeskAI/browser-tools-mcp)
- [问题反馈](https://github.com/AgentDeskAI/browser-tools-mcp/issues)