// Test script to verify the extension connection fixes
// This can be run in the browser console to test the extension

console.log("Testing Browser Tools Extension Connection...");

// Test 1: Check if service worker is responding
function testServiceWorkerHealth() {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage({ type: "HEALTH_CHECK" }, (response) => {
      if (chrome.runtime.lastError) {
        console.error("❌ Service Worker Health Check Failed:", chrome.runtime.lastError.message);
        resolve(false);
      } else {
        console.log("✅ Service Worker Health Check Passed:", response);
        resolve(true);
      }
    });
  });
}

// Test 2: Test URL retrieval
function testUrlRetrieval() {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage({ 
      type: "GET_CURRENT_URL", 
      tabId: chrome.devtools?.inspectedWindow?.tabId || 1 
    }, (response) => {
      if (chrome.runtime.lastError) {
        console.error("❌ URL Retrieval Failed:", chrome.runtime.lastError.message);
        resolve(false);
      } else {
        console.log("✅ URL Retrieval Passed:", response);
        resolve(true);
      }
    });
  });
}

// Test 3: Test server validation
function testServerValidation() {
  return new Promise((resolve) => {
    // This would normally be called internally, but we can test the message passing
    chrome.runtime.sendMessage({ 
      type: "SERVER_VALIDATION_SUCCESS",
      serverInfo: { name: "Test", version: "1.0" },
      serverHost: "localhost",
      serverPort: 3025
    }, (response) => {
      if (chrome.runtime.lastError) {
        console.error("❌ Server Validation Message Failed:", chrome.runtime.lastError.message);
        resolve(false);
      } else {
        console.log("✅ Server Validation Message Passed");
        resolve(true);
      }
    });
  });
}

// Run all tests
async function runAllTests() {
  console.log("🔧 Starting Extension Connection Tests...");
  
  const healthTest = await testServiceWorkerHealth();
  const urlTest = await testUrlRetrieval();
  const validationTest = await testServerValidation();
  
  const allPassed = healthTest && urlTest && validationTest;
  
  console.log("\n📊 Test Results:");
  console.log(`Service Worker Health: ${healthTest ? "✅ PASS" : "❌ FAIL"}`);
  console.log(`URL Retrieval: ${urlTest ? "✅ PASS" : "❌ FAIL"}`);
  console.log(`Message Passing: ${validationTest ? "✅ PASS" : "❌ FAIL"}`);
  console.log(`\nOverall: ${allPassed ? "✅ ALL TESTS PASSED" : "❌ SOME TESTS FAILED"}`);
  
  if (allPassed) {
    console.log("🎉 Extension connection is working properly!");
  } else {
    console.log("⚠️ Extension may have connection issues. Check the console for errors.");
  }
}

// Auto-run tests if this script is executed
if (typeof window !== 'undefined') {
  runAllTests();
}
